{"name": "blip", "engines": {"node": "20.8.0"}, "packageManager": "yarn@3.6.4", "version": "1.89.0-rc.4", "private": true, "scripts": {"test": "TZ=UTC NODE_ENV=test yarn jest --verbose --runInBand; JEST_EXIT_CODE=$?; TZ=UTC NODE_ENV=test NODE_OPTIONS='--max-old-space-size=4096' yarn karma start; KARMA_EXIT_CODE=$?; [ $JEST_EXIT_CODE -eq 0 ] && [ $KARMA_EXIT_CODE -eq 0 ]", "test:concurrent": "concurrently \"TZ=UTC NODE_ENV=test yarn jest --verbose --runInBand\" \"TZ=UTC NODE_ENV=test NODE_OPTIONS='--max-old-space-size=4096' yarn karma start\" --group --success=all", "test:karma": "TZ=UTC NODE_ENV=test NODE_OPTIONS='--max-old-space-size=4096' yarn karma start", "test:jest": "TZ=UTC NODE_ENV=test yarn jest --verbose --runInBand", "pretest": "NODE_ENV=test npm run lint", "browser-tests": "TZ=UTC NODE_ENV=test yarn karma start --browsers Chrome", "test-watch": "WATCH_MODE=1 TZ=UTC NODE_ENV=test NODE_OPTIONS='--max-old-space-size=4096' yarn karma start --no-single-run --reporters=mocha", "start": "NODE_ENV=development NODE_OPTIONS='--max-old-space-size=4096' yarn webpack-dev-server --color --progress --port 3000 --host 0.0.0.0", "watchViz": "export NODE_OPTIONS='--max-old-space-size=4096' && cd ${TIDEPOOL_DOCKER_VIZ_DIR:-'../viz'} && npm run start", "cleanViz": "yarn rimraf ${TIDEPOOL_DOCKER_VIZ_DIR:-'../viz'}/dist/*", "startWithViz": "npm run cleanViz; yarn concurrently --kill-others --names viz,blip \"npm run watchViz\" \"while ! test -f ${TIDEPOOL_DOCKER_VIZ_DIR:-'../viz'}'/dist/index.js'; do sleep 1; done; npm run start\"", "startLocal": "node -e \"require('./config/local')?.linkedPackages?.['@tidepool/viz'] ? require('child_process').spawn('npm', ['run', 'startWithViz'], { stdio: 'inherit' }) : require('child_process').spawn('npm', ['run', 'start'], { stdio: 'inherit' })\"", "build": "NODE_OPTIONS='--max-old-space-size=4096' NODE_ENV=production npm run build-app && npm run build-config", "build-app": "NODE_ENV=production node buildapp", "build-config": "NODE_ENV=production node buildconfig", "server": "node server", "update-translations": "i18next 'app/**/*.js' 'node_modules/tideline/{plugins,js}/**/*.js' \"${TIDEPOOL_DOCKER_VIZ_DIR:-'../viz'}/{storiesDatatypes,storybookDatatypes,stories,src}/**/*.js\" -c i18next-parser.config.json -o .", "storybook": "NODE_ENV=test yarn sb dev -p 6006 --ci", "build-storybook": "build-storybook", "lint": "yarn eslint app stories test __tests__", "lint:jest": "yarn eslint __tests__"}, "dependencies": {"body-parser": "1.20.3", "custom-protocol-check": "1.4.0", "express": "4.21.2", "helmet": "3.23.3", "i18n-iso-countries": "7.7.0", "plotly.js-basic-dist-min": "2.27.0", "rollbar": "2.26.2"}, "devDependencies": {"@babel/cli": "7.23.0", "@babel/core": "7.23.0", "@babel/eslint-parser": "7.22.15", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@babel/plugin-transform-modules-commonjs": "7.23.0", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.23.2", "@babel/preset-react": "7.22.15", "@babel/runtime": "7.23.1", "@emotion/babel-plugin": "11.3.0", "@emotion/cache": "11.5.0", "@emotion/react": "11.5.0", "@emotion/styled": "11.3.0", "@material-ui/core": "4.12.4", "@material-ui/icons": "4.11.3", "@material-ui/lab": "4.0.0-alpha.61", "@octokit/rest": "21.1.0", "@pmmmwh/react-refresh-webpack-plugin": "0.5.11", "@react-keycloak/web": "3.4.0", "@storybook/addon-a11y": "7.5.1", "@storybook/addon-actions": "7.5.1", "@storybook/addon-designs": "7.0.5", "@storybook/addon-essentials": "7.5.0", "@storybook/addon-knobs": "7.0.2", "@storybook/addon-links": "7.5.1", "@storybook/addons": "7.5.0", "@storybook/cli": "7.5.0", "@storybook/react": "7.5.0", "@storybook/react-webpack5": "7.5.0", "@swc/core": "1.11.24", "@swc/jest": "0.2.38", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.6.1", "@tidepool/viz": "1.50.0-rc.1", "async": "2.6.4", "autoprefixer": "10.4.16", "babel-core": "7.0.0-bridge.0", "babel-eslint": "9.0.0", "babel-loader": "9.1.3", "babel-plugin-istanbul": "6.1.1", "babel-plugin-rewire": "1.2.0", "babel-plugin-styled-components": "2.1.4", "babel-preset-react-app": "10.0.1", "bows": "1.7.2", "browser-locale": "1.0.3", "browserify-zlib": "0.2.0", "buffer": "6.0.3", "canonicalize": "2.0.0", "chai": "4.3.10", "chromedriver": "135.0.2", "classnames": "2.3.2", "concurrently": "8.2.2", "connected-react-router": "6.9.3", "copy-webpack-plugin": "11.0.0", "core-js": "3.42.0", "create-react-class": "15.7.0", "crypto-hash": "3.0.0", "crypto-js": "4.2.0", "css-loader": "6.8.1", "css-minimizer-webpack-plugin": "5.0.1", "custom-protocol-detection": "1.0.1", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.7", "eslint": "8.51.0", "eslint-config-airbnb": "19.0.4", "eslint-plugin-import": "2.28.1", "eslint-plugin-jest-dom": "5.5.0", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-lodash": "7.4.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-testing-library": "7.1.1", "file-loader": "6.2.0", "formik": "2.4.5", "formik-persist-values": "1.4.1", "history": "4.10.1", "html-webpack-plugin": "5.5.3", "i18next": "23.6.0", "i18next-parser": "8.9.0", "immutability-helper": "3.1.1", "intl": "1.2.5", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "karma": "6.4.2", "karma-chai": "0.1.0", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-intl-shim": "1.0.3", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-sinon": "1.0.5", "karma-sourcemap-loader": "0.4.0", "karma-webpack": "5.0.0", "keycloak-js": "22.0.4", "launchdarkly-react-client-sdk": "3.0.9", "less": "4.2.0", "less-loader": "11.1.3", "lodash": "4.17.21", "material-ui-popup-state": "1.9.3", "mini-css-extract-plugin": "2.7.6", "mocha": "10.2.0", "moment": "2.29.4", "moment-timezone": "0.5.43", "ms": "2.1.3", "object-invariant-test-helper": "0.1.1", "optional": "0.1.4", "postcss": "8.4.31", "postcss-loader": "7.3.3", "process": "0.11.10", "prop-types": "15.8.1", "qhistory": "1.1.0", "qs": "6.11.2", "react": "16.14.0", "react-dates": "21.8.0", "react-dom": "16.14.0", "react-i18next": "13.3.1", "react-input-mask": "3.0.0-alpha.2", "react-loading": "2.0.3", "react-moment-proptypes": "1.8.1", "react-redux": "8.1.3", "react-refresh": "0.14.0", "react-router-dom": "5.3.0", "react-scroll": "1.9.0", "react-scroll-to-top": "3.0.0", "react-select": "5.7.7", "react-window-size-listener": "1.5.4", "readable-stream": "4.4.2", "redux": "4.2.1", "redux-cache": "0.3.0", "redux-devtools": "3.7.0", "redux-devtools-dock-monitor": "1.2.0", "redux-devtools-log-monitor": "2.1.0", "redux-immutable-state-invariant": "2.1.0", "redux-logger": "3.0.6", "redux-mock-store": "1.5.4", "redux-thunk": "2.4.2", "reselect": "4.1.8", "rimraf": "5.0.5", "rollbar-sourcemap-webpack-plugin": "3.3.0", "salinity": "0.0.8", "script-loader": "0.7.2", "shelljs": "0.8.5", "sinon": "17.0.0", "source-map-loader": "4.0.1", "style-loader": "3.3.3", "styled-components": "6.1.0", "stylelint": "15.11.0", "stylelint-config-recommended": "13.0.0", "stylelint-config-styled-components": "0.1.1", "sundial": "1.7.1", "terser": "5.22.0", "terser-webpack-plugin": "5.3.9", "theme-ui": "0.16.1", "tideline": "1.36.0-rc.1", "tidepool-platform-client": "0.64.0-rc.3", "tidepool-standard-action": "0.1.1", "ua-parser-js": "1.0.36", "url-loader": "4.1.1", "util": "0.12.5", "webpack": "5.94.0", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.1", "yup": "1.3.2"}, "peerDependencies": {"babel-core": "6.x || ^7.0.0-bridge.0", "classnames": "2.x", "react": "16.x", "react-dom": "16.x", "react-redux": "8.x", "redux": "4.x"}, "resolutions": {"express": "4.21.2", "@emotion/react": "11.5.0", "@emotion/styled": "11.3.0", "crypto-js": "4.2.0", "lodash": "4.17.21", "qs": "6.11.2", "react-sizeme": "2.6.12", "webpack": "5.94.0"}}